{"prettier.enable": false, "editor.formatOnSave": false, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.fixAll.stylelint": "explicit", "source.organizeImports": "never"}, "eslint.rules.customizations": [{"rule": "style/*", "severity": "off", "fixable": true}, {"rule": "format/*", "severity": "off", "fixable": true}, {"rule": "*-indent", "severity": "off", "fixable": true}, {"rule": "*-spacing", "severity": "off", "fixable": true}, {"rule": "*-spaces", "severity": "off", "fixable": true}, {"rule": "*-order", "severity": "off", "fixable": true}, {"rule": "*-dangle", "severity": "off", "fixable": true}, {"rule": "*-newline", "severity": "off", "fixable": true}, {"rule": "*quotes", "severity": "off", "fixable": true}, {"rule": "*semi", "severity": "off", "fixable": true}], "stylelint.validate": ["css", "scss", "vue"], "eslint.validate": ["javascript", "javascriptreact", "typescript", "typescriptreact", "vue", "html", "markdown", "json", "jsonc", "yaml", "toml", "xml", "gql", "graphql", "astro", "svelte", "css", "less", "scss", "pcss", "postcss"], "i18n-ally.localesPaths": ["apps/*/src/locales/lang"], "i18n-ally.displayLanguage": "zh-cn", "i18n-ally.editor.preferEditor": true, "i18n-ally.keystyle": "nested", "i18n-ally.keepFulfilled": true, "i18n-ally.indent": 2, "i18n-ally.sortKeys": true, "i18n-ally.enabledFrameworks": ["vue"], "commentTranslate.targetLanguage": "zh-CN", "commentTranslate.source": "Google", "commentTranslate.multiLineMerge": true, "github-actions.remote-name": "github"}
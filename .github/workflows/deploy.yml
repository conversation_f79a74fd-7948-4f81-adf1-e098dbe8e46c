name: Deploy

on:
  workflow_dispatch:
  push:
    branches:
      - master

jobs:
  deploy-server:
    name: Deploy static to server
    permissions:
      id-token: write
      contents: write

    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install pnpm
        uses: pnpm/action-setup@v2

      - name: Use Node.js v18
        uses: actions/setup-node@v3
        with:
          node-version: 18
          registry-url: https://registry.npmjs.org/
          cache: pnpm

      - run: pnpm install

      - name: Build Packages
        run: pnpm --filter=./apps/rbac run build

      - name: Deploy to Server
        uses: easingthemes/ssh-deploy@main
        with:
          SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
          ARGS: "-lahzv"
          SOURCE: "./apps/rbac/rbac"
          REMOTE_HOST: ${{ secrets.REMOTE_HOST }}
          REMOTE_USER: ${{ secrets.REMOTE_USER }}
          TARGET: ${{ secrets.REMOTE_TARGET }}


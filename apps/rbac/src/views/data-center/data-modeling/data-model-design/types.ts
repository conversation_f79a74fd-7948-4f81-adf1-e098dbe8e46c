export interface CurrentNode {
  nodeId?: string
  nodeType?: string
  projectId?: number
}

export interface Query {
  orgId: string
}

export interface DataModelItem {
  /** 主键id */
  id?: number
  /** 模型名称 */
  modelName?: string
  /** 模型表名 */
  modelTableName?: string
  /** 建模模式 */
  modelingMode?: string
  /** 数仓分层 */
  dwLayer?: string
  /** 创建时间 */
  createTime?: string
  /** 发布状态 */
  publishStatus?: string
}

export interface TableFieldItem {
  /** 主键id */
  id?: string | number
  /** 名称 */
  name?: string
  /** 字段名 */
  fieldName?: string
  /** 类型 */
  type?: string
  /** 数据库类型 */
  dbType?: string
  /** 字段长度 */
  length?: number
  /** 小数长度 */
  decLength?: number
  /** 主键 */
  isPrimaryKey?: boolean
  /** 非空 */
  isNotEmpty?: boolean
  /** 自增 */
  isAutoInc?: boolean
  /** 是否分区 */
  isPartition?: boolean
  /** 去重 */
  isUnique?: boolean
  /** 分桶 */
  bucket?: number
  // /** 敏感信息 */
  // isSensitiveInfo?: boolean
  // /** 搜索项 */
  // isSearchItem?: boolean
  /** 默认值 */
  defaultVal?: string
  /** 字段说明 */
  comment?: string
}

export interface IndexDataItem {
  id: string | number
  indexName: string
  indexType?: string
  fields: string[]
}

export interface FormState {
  dataSource: number | undefined
  dwLayer: number | undefined
  modelName: string
  remark: string
  modelMode: number | undefined
  prefix: number | undefined
  newTableName: string
  modelTableName: string
  SQLCreateTable: boolean
  SQLScript: string
}

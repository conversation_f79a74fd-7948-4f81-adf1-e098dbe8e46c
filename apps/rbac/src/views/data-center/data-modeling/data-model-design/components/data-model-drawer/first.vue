<script setup lang="tsx">
import type { SelectProps } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';
import type { FormExpose } from 'ant-design-vue/es/form/Form';
import type { FormState } from '../../types';
import { reactive } from 'vue';
import eventBus from '@/utils/eventBus';

const props = defineProps<{
  initState: API.DataWarehouseModel | null
  isEdit: boolean
}>();

const formState = reactive<FormState>({
  dataSource: undefined,
  dwLayer: undefined,
  modelName: '',
  remark: '',
  modelMode: 1,
  prefix: undefined,
  newTableName: '',
  modelTableName: '',
  SQLCreateTable: false,
  SQLScript: '',
});

const isEdit = computed(() => props.isEdit);

const rules: Record<string, Rule[]> = {
  dataSource: [{ required: true, message: '请选择数据源', trigger: 'change' }],
  dwLayer: [{ required: true, message: '请选择数仓分层', trigger: 'change' }],
  modelName: [{ required: true, message: '请输入模型名称', trigger: 'blur' }],
  modelMode: [{ required: true, message: '请选择建模模式', trigger: 'change' }],
  prefix: [{ required: true, message: '请选择前缀', trigger: 'change' }],
  newTableName: [{ required: true, message: '请输入新建表名', trigger: 'blur' }],
  modelTableName: [{ required: true, message: '请输入模型表名', trigger: 'blur' }],
};

const dataSourceOptions = ref<SelectProps['options']>([]);
const dwLayerOptions = ref<SelectProps['options']>([]);
const prefixOptions = ref<SelectProps['options']>([]);
const formRef = ref<FormExpose>();

// async function getDataSourceOptions() {
//   const res = await getDataWarehouseModelDataSources();
//   if (res.success) {
//     dataSourceOptions.value = res.data?.map(item => ({
//       label: item.name,
//       value: item.id,
//       sourceType: item.sourceType,
//     })) || [];

//     const tempCurrentDataSourceType = dataSourceOptions.value.find(item => item.value === props.initState?.dbSourceId)?.sourceType;
//     if (tempCurrentDataSourceType) {
//       eventBus.emit('init-data-source-change', {
//         sourceType: tempCurrentDataSourceType,
//       });
//     }
//   }
// }

async function getDwLayerOptions() {
  const res = await postDataWarehouseLayerAll({});
  if (res.success) {
    dwLayerOptions.value = [];
    (res.data || []).forEach((item) => {
      (item?.list || []).forEach((child) => {
        dwLayerOptions.value?.push({
          label: child.name,
          value: child.id,
        });
      });
    });
  }
}

async function getPrefixOptions(isEditInit = false) {
  const res = await postDataWarehouseLayerRules({
    layerid: formState.dwLayer!,
  });
  if (res.success) {
    prefixOptions.value = res.data?.map(item => ({
      label: `${item.tableprefix}_${item.tablenfix}`,
      value: item.id,
      tableprefix: item.tableprefix,
      tablenfix: item.tablenfix,
    })) || [];

    if (!isEditInit) {
      formState.prefix = undefined;
      assembleModalTable();
    }
  }
}

async function getDataSourceOptions(isEditInit = false) {
  const res = await postDataWarehouseModelDataSourcesByLayerId({
    id: formState.dwLayer!,
  });
  if (res.success) {
    dataSourceOptions.value = res.data?.map(item => ({
      label: item.name,
      value: item.id,
      sourceType: item.sourceType,
    })) || [];

    if (!isEditInit) {
      formState.dataSource = undefined;
    }
    else {
      const tempCurrentDataSourceType = dataSourceOptions.value.find(item => item.value === props.initState?.dbSourceId)?.sourceType;
      if (tempCurrentDataSourceType) {
        eventBus.emit('init-data-source-change', {
          sourceType: tempCurrentDataSourceType,
        });
      }
    }
  }
}

function handleDwLayerChange(isEditInit = false) {
  getPrefixOptions(isEditInit);
  getDataSourceOptions(isEditInit);
}

function assembleModalTable() {
  if (!formState.prefix || !formState.newTableName) {
    formState.modelTableName = '';
    return;
  }

  const prefixItem = prefixOptions.value?.find(item => item.value === formState.prefix);

  formState.modelTableName = `${prefixItem?.label}_${formState.newTableName}`;
}

async function validate(needValidate = true) {
  if (needValidate) {
    await formRef.value?.validate();
  }
  return formState;
}

defineExpose({
  validate,
});

async function init() {
  getDwLayerOptions();
  // 编辑回显时初始化表单数据
  if (props.initState) {
    Object.assign(formState, {
      dataSource: props.initState.dbSourceId,
      dwLayer: props.initState.layerid,
      modelName: props.initState.modelName,
      remark: props.initState.remark,
      modelMode: props.initState.createType,
      prefix: Number(props.initState.tablePrefix),
      newTableName: props.initState.tableNameTemp,
      modelTableName: props.initState.tableName,
      SQLCreateTable: false,
      SQLScript: '',
    });

    const isEditInit = true;
    handleDwLayerChange(isEditInit);
  }
}

onMounted(() => {
  init();
});

function handleDataSourceChange() {
  if (!formState.dataSource) {
    return;
  }

  const dataSourceOption = dataSourceOptions.value?.find(option => option.value === formState.dataSource);
  if (dataSourceOption) {
    eventBus.emit('data-source-change', {
      sourceType: dataSourceOption.sourceType,
    });
  }
}
</script>

<template>
  <a-form
    ref="formRef"
    layout="vertical"
    :model="formState"
    :rules="rules"
  >
    <div class="mt-[30px]">
      <div class="font-['PingFang SC'] font-[500] text-[18px] leading-[21px] text-[#161E5D]">
        <FileTextOutlined />
        基本信息
      </div>
      <a-divider />
      <div>
        <a-form-item label="数仓分层" name="dwLayer">
          <a-select
            v-model:value="formState.dwLayer"
            placeholder="请选择内容"
            :disabled="isEdit"
            :options="dwLayerOptions"
            @change="() => handleDwLayerChange()"
          />
        </a-form-item>
        <a-form-item label="数据源" name="dataSource">
          <a-select v-model:value="formState.dataSource" :disabled="isEdit" placeholder="请选择内容" :options="dataSourceOptions" @change="handleDataSourceChange" />
        </a-form-item>
        <a-form-item label="模型名称" name="modelName">
          <a-input v-model:value="formState.modelName" placeholder="请输入内容" :maxlength="30" />
        </a-form-item>
        <a-form-item label="备注" name="remark">
          <a-textarea v-model:value="formState.remark" placeholder="请输入内容" />
        </a-form-item>
      </div>
    </div>
    <div class="mt-[30px]">
      <div class="font-['PingFang SC'] font-[500] text-[18px] leading-[21px] text-[#161E5D]">
        <FileTextOutlined />
        模型配置
      </div>
      <a-divider />
      <div>
        <a-form-item label="建模模式" name="modelMode">
          <a-select
            v-model:value="formState.modelMode"
            placeholder="请选择内容"
            :disabled="isEdit"
            :options="[
              // {
              //   label: '仅仅创建模型',
              //   value: 0,
              // },
              {
                label: '创建模型及物理表',
                value: 1,
              },
              // {
              //   label: '已有物理表仅创建模型',
              //   value: 2,
              // },
            ]"
          />
        </a-form-item>
        <a-form-item label="选择前缀" name="prefix">
          <a-select v-model:value="formState.prefix" :disabled="isEdit" placeholder="请选择内容" :options="prefixOptions" @change="() => assembleModalTable()" />
        </a-form-item>
        <a-form-item label="新建表名" name="newTableName">
          <a-input v-model:value="formState.newTableName" :disabled="isEdit" placeholder="请输入内容" :maxlength="30" @change="() => assembleModalTable()" />
        </a-form-item>
        <a-form-item label="模型表名" name="modelTableName">
          <a-input v-model:value="formState.modelTableName" :disabled="true" placeholder="请输入内容" :maxlength="30" />
        </a-form-item>
        <!-- <a-form-item label="" name="SQLCreateTable">
          <a-checkbox v-model:checked="formState.SQLCreateTable" :disabled="isEdit">
            SQL快速建表
          </a-checkbox>
        </a-form-item>    -->
        <a-form-item label="" name="SQLScript">
          <a-textarea v-if="formState.SQLCreateTable" v-model:value="formState.SQLScript" :disabled="isEdit" :rows="4" placeholder="请输入内容" class="SQL-box" />
        </a-form-item>
      </div>
    </div>
  </a-form>
</template>

<style lang="scss" scoped>
  .SQL-box {
    color: rgb(255 255 255 / 50%);
    background-color: #38496c;
  }

  .SQL-box::placeholder {
    color: rgb(255 255 255 / 50%);
  }
</style>

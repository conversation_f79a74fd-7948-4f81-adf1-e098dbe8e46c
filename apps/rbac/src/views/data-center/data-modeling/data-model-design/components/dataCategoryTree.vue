<script setup lang="ts">
import type { SelectProps } from 'ant-design-vue';
import type { DataNode } from 'ant-design-vue/es/tree';
import { useToggle } from '@vueuse/core';

interface DataProject {
  id: number
  en_name: string
  zh_name: string
}

defineOptions({
  name: 'DataCategoryTree',
});

const emit = defineEmits(['select']);

const treeData = ref<DataNode[]>([]);
const dataList: DataNode[] = [];

const projectId = ref<number>();
const projectList = ref<SelectProps['options']>([]);

const searchValue = ref('');
function getParentKey(key: string | number,
  tree: DataNode[]): string | number | undefined {
  let parentKey;
  for (let i = 0; i < tree.length; i++) {
    const node = tree[i];
    if (node.children) {
      if (node.children.some(item => item.key === key)) {
        parentKey = node.key;
      }
      else if (getParentKey(key, node.children)) {
        parentKey = getParentKey(key, node.children);
      }
    }
  }
  return parentKey;
}
const expandedKeys = ref<(string | number)[]>([]);
const autoExpandParent = ref<boolean>(true);
const [loading, setLoading] = useToggle(false);
const selectedKeys = ref<(string | number)[]>([]);

function onExpand(keys: string[]) {
  expandedKeys.value = keys;
  autoExpandParent.value = false;
}

watch(searchValue, (value) => {
  const expanded = dataList?.map((item) => {
    if (value && item.title.includes(value)) {
      return getParentKey(item.key, treeData.value);
    }
    return null;
  })
    .filter((item, i, self) => item && self.indexOf(item) === i);

  expandedKeys.value = expanded as (string | number)[];
  searchValue.value = value;
  autoExpandParent.value = true;
});

function generateList(data: DataNode[]) {
  for (let i = 0; i < data.length; i++) {
    const node = data[i];
    const key = node.key;
    const title = node.title;
    dataList.push({ key, title });
    if (node.children) {
      generateList(node.children);
    }
  }
}

function onSelect(selectedKeys: string[], info: any) {
  let nodeType;
  const nodeId = info.node.key.split('-')[1];

  if (info.node.key.includes('folder')) {
    nodeType = 'first';
  }
  else {
    nodeType = 'second';
  }

  emit('select', {
    nodeType,
    nodeId,
    projectId: projectId.value,
  });
}

async function onLoadData() {
  const res = await postDataWarehouseLayerAll({
    projectId: projectId.value,
  });

  treeData.value = res.data?.map((item) => {
    return {
      title: item.typeCodeName,
      key: `folder-${item.typeCode}`,
      children: item.list?.map((child) => {
        return {
          // title: `${child.secondTypeCodeName}${child.secondTypeCode}`,
          title: `${child.name}`,
          key: `file-${child.id}`,
        };
      }) || [],
    };
  }) || [];

  generateList(treeData.value);

  // 选中第一个节点
  if (treeData.value.length > 0) {
    const firstNode = treeData.value[0];
    selectedKeys.value = [firstNode.key as string];
    onSelect([firstNode.key as string], { node: firstNode });
  }
}

async function getProjectList() {
  const res = await getDataArchitectureDataProjectList();
  projectList.value = res.data?.map((item: DataProject) => ({
    label: item.zh_name,
    value: item.id,
  })) ?? [];

  if (projectList.value?.length) {
    projectId.value = projectList.value[0].value as unknown as number;
    onProjectChange();
  }
}

function onProjectChange() {
  onLoadData();
}

onMounted(() => {
  getProjectList();
  // onLoadData();
});
</script>

<template>
  <a-card :body-style="{ padding: '0px' }" class="w-full h-full overflow-y-auto overflow-x-hidden">
    <div class="px-[24px] py-[19px] bg-[#F8F9FF] text-[16px] text-[#161E5D] font-[600] leading-[19px]">
      数据目录
    </div>
    <div class="px-[16px] py-[20px]">
      <a-select
        v-model:value="projectId"
        placeholder="请选择数据板块"
        :options="projectList"
        allow-clear
        style="margin-bottom: 20px;"
        class="w-full"
        @change="onProjectChange"
      />
      <a-input v-model:value="searchValue" placeholder="请输入内容" class="mb-[20px]">
        <template #prefix>
          <SearchOutlined />
        </template>
      </a-input>
      <a-spin :spinning="loading">
        <a-empty v-if="treeData?.length === 0" />
        <a-tree
          v-else
          v-bind="$attrs"
          block-node
          show-icon
          :tree-data="treeData"
          :expanded-keys="expandedKeys"
          :selected-keys="selectedKeys"
          :auto-expand-parent="autoExpandParent"
          @expand="(keys) => { onExpand(keys as string[]); }"
          @select="(keys, info) => { selectedKeys = keys as string[]; onSelect(keys as string[], info); }"
        >
          <template #title="node">
            <div class="flex w-full items-center overflow-hidden">
              <PubSvgIcon v-if="node.key.indexOf('folder') !== -1" size="20px" name="icon-tree-folder" class="mr-[10px]" />
              <PubSvgIcon v-if="node.key.indexOf('file') !== -1" size="20px" name="icon-tree-file" class="mr-[10px]" />
              <!-- <span :title="node.title" class="truncate">{{ node.title }}</span> -->
              <span v-if="node.title.indexOf(searchValue) > -1">
                {{ node.title.substring(0, node.title.indexOf(searchValue)) }}
                <span class="text-[#f50]">{{ searchValue }}</span>
                {{ node.title.substring(node.title.indexOf(searchValue) + searchValue.length) }}
              </span>
              <span v-else>{{ node.title }}</span>
            </div>
          </template>
          <!-- <template #icon="{ key, selected }">
            <PubSvgIcon size="20px" name="icon-tree-folder" />
          </template> -->
        </a-tree>
      </a-spin>
    </div>
  </a-card>
</template>

<style scoped>
:deep(.ant-tree .ant-tree-node-content-wrapper) {
  overflow: hidden;
}
</style>

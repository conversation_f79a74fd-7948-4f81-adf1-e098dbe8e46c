<script setup lang="tsx">
import type { ProColumns } from '@pubinfo/pro-components';
import type { SelectProps } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';
import type { secondStepForm, TableFieldItem } from '../types';

const props = defineProps<{
  initData: secondStepForm
  usedCount: number
  // typeOptions: SelectProps['options']
}>();

const tableRef = ref();
const formRef = ref();

type EditableData = Record<string, TableFieldItem>;
const editableData: EditableData = reactive({});

const fieldList = computed<TableFieldItem[]>(() => {
  return Object.values(editableData);
});

const typeOptions = ref<SelectProps['options']>([
  // {
  //   label: '时间戳',
  //   value: '时间戳',
  //   dbType: 'TIMESTAMP',
  // },
  {
    label: '字符串',
    value: '字符串',
    dbType: 'STRING',
  },
  {
    label: '整数',
    value: '整数',
    dbType: 'INT',
  },
]);

const rules = computed(() => {
  const formRules: Record<string, Record<string, Rule[]>> = {};

  fieldList.value.forEach((item) => {
    formRules[item.id!] = {
      name: [{ required: true, message: '请输入名称', trigger: 'blur' }],
      fieldName: [{ required: true, message: '请输入字段名', trigger: 'blur' }],
      type: [{ required: true, message: '请选择类型', trigger: 'change' }],
    };
  });

  return formRules;
});

const columns: ProColumns<TableFieldItem> = [
  {
    width: 80,
    title: '序号',
    hideInSearch: true,
    dataIndex: 'index',
    fixed: 'left',
  },
  {
    width: 150,
    title: '名称',
    hideInSearch: true,
    dataIndex: 'name',
  },
  {
    width: 150,
    title: '字段名',
    hideInSearch: true,
    dataIndex: 'fieldName',
  },
  {
    width: 150,
    title: '类型',
    hideInSearch: true,
    dataIndex: 'type',
  },
  {
    width: 150,
    title: '数据库类型',
    hideInSearch: true,
    dataIndex: 'dbType',
  },
  {
    title: '操作',
    hideInSearch: true,
    dataIndex: 'action',
  },
];

function deleteData(key: string) {
  if (editableData[key]) {
    delete editableData[key];
  }
}

function getUUID() {
  const timestamp = new Date().getTime();
  const random = Math.floor(Math.random() * 10000);
  return `custom-${timestamp}-${random.toString().padStart(4, '0')}`;
}

function addData() {
  const newItem = {
    id: getUUID(),
    name: '',
    fieldName: '',
    type: '',
    dbType: '',
  };
  editableData[newItem.id] = newItem;
}

// 根据类型和数据源更新 dbType
function updateDbType(record: TableFieldItem) {
  if (!record.type) {
    return;
  }

  const typeOption = typeOptions.value?.find(option => option.value === record.type);
  if (!typeOption) {
    return;
  }

  record.dbType = typeOption.dbType;
}

function init() {
  if (props.initData) {
    const { messageBody = [] } = props.initData;

    messageBody.forEach((item) => {
      editableData[item.id!] = {
        id: item.id!,
        name: item.name,
        fieldName: item.fieldName,
        type: item.type,
        dbType: item.dbType,
      };
    });
  }
}

async function getFormData(needValidate = true) {
  if (needValidate) {
    await formRef.value?.validate();
  }
  return {
    messageBody: fieldList.value,
  };
}

defineExpose({
  getFormData,
});

// 监听数据源变化事件
onMounted(() => {
  init();
});
</script>

<template>
  <div>
    <!-- <a-space class="mt-[31px] mb-[24px]">

    </a-space> -->
    <a-form
      ref="formRef"
      :model="editableData"
      :rules="rules"
    >
      <a-table
        ref="tableRef"
        row-key="id"
        :data-source="fieldList"
        :columns="columns"
        :toolbar="false"
        :search="false"
        :pagination="false"
      >
        <template #headerCell="{ column }">
          <template v-if="['name', 'fieldName', 'type'].includes(column.dataIndex as 'name' | 'fieldName' | 'type')">
            <span class="text-red-500 font-[SimSun,sans-serif]">*</span>
            <span>{{ column.title }}</span>
          </template>
          <template v-else>
            {{ column.title }}
          </template>
        </template>
        <template #bodyCell="{ column, index, record }">
          <template v-if="['name', 'fieldName', 'defaultVal', 'comment'].includes(column.dataIndex as 'name' | 'fieldName' | 'defaultVal' | 'comment')">
            <a-form-item :name="[record.id, column.dataIndex]">
              <a-input
                v-model:value="editableData[record.id][column.dataIndex as 'name' | 'fieldName']"
                :disabled="usedCount > 0 && !record.id.includes('custom')"
              />
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'type'">
            <a-form-item :name="[record.id, 'type']">
              <a-select
                v-model:value="editableData[record.id].type"
                class="w-full"
                :disabled="usedCount > 0 && !record.id.includes('custom')"
                :options="typeOptions"
                @change="() => updateDbType(editableData[record.id])"
              />
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'index'">
            <a-form-item :name="[record.id, column.dataIndex]">
              {{ index + 1 }}
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'dbType'">
            <a-form-item :name="[record.id, column.dataIndex]">
              {{ record.dbType || '-' }}
            </a-form-item>
          </template>
          <template v-if="column.dataIndex === 'action'">
            <a-space>
              <a-button type="primary" danger :disabled="usedCount > 0 && !record.id.includes('custom')" @click="deleteData(record.id)">
                删除
              </a-button>
            </a-space>
          </template>
        </template>
        <template #footer>
          <div class="flex items-center justify-center text-[#2469F1] cursor-pointer" @click="addData">
            <PlusOutlined />  新增字段
          </div>
        </template>
      </a-table>
    </a-form>
  </div>
</template>

<!-- <style lang="scss" scoped>

</style> -->

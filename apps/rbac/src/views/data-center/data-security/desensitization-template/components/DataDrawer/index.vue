<script setup lang="tsx">
import type { OptionProps } from 'ant-design-vue/es/select';
import type { DataDrawerForm } from '../../types';
import { SchemaForm } from '@pubinfo/pro-components';
import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';
import CustomDrawer from '@/components/CustomDrawer/index.vue';
import dataCenterMenu from '@/router/modules/data-center/index.menu';
import { numberPattern } from '@/utils/validatorUtils';
import { dataDrawerConfigKey } from '../../types';

const props = defineProps({
  type: String,
});
const emit = defineEmits(['submit']);
const dataDrawerConfig = inject(dataDrawerConfigKey);
const open = defineModel<boolean>('open');
const defaultFormData: DataDrawerForm = {
  /** 名称 */
  name: '',
  /** 脱敏模块 */
  desensitizationModuleList: [],

};
const form: Ref<DataDrawerForm> = ref(cloneDeep(defaultFormData));
const sensitiveLabelIdOptions: Ref<OptionProps[]> = ref([]);
const securityLevelIdOptions: Ref<OptionProps[]> = ref([]);
const desensitizationModuleListOptions: Ref<OptionProps[]> = ref([]);
const algorithmIdOptions: Ref<OptionProps[]> = ref([]);
const maskingRuleHidden = computed(() => {
  return !form.value.desensitizationMethod || form.value.desensitizationMethod === 10;
});
function getDesensitizationModuleList() {
  desensitizationModuleListOptions.value = dataCenterMenu.map((item) => {
    return {
      value: item.meta ? item.meta.title : '未定义名称',
      label: item.meta ? item.meta.title : '未定义名称',
    };
  });
}
getDesensitizationModuleList();
const columns = ref([
  {
    valueType: 'a-input',
    key: 'name',
    label: '脱敏规则名称',
    rules: [{ required: true, message: '请输入', trigger: 'blur' }],
    fieldProps: {
      placeholder: '请输入',
      maxlength: 40,
    },
  },
  {
    valueType: 'a-select',
    key: 'sensitiveLabelId',
    label: '敏感标签',
    rules: [{ required: true, message: '请选择', trigger: 'blur' }],
    fieldProps: {
      placeholder: '请选择',
      options: sensitiveLabelIdOptions,
    },
  },
  {
    valueType: 'a-select',
    key: 'securityLevelId',
    label: '密级',
    rules: [{ required: true, message: '请选择', trigger: 'blur' }],
    fieldProps: {
      placeholder: '请选择',
      options: securityLevelIdOptions,
      fieldNames: {
        label: 'level',
        value: 'id',
      },
    },
  },
  {
    valueType: 'a-select',
    key: 'desensitizationMethod',
    label: '脱敏方式',
    rules: [{ required: true, message: '请选择', trigger: 'blur' }],

    fieldProps: {
      placeholder: '请选择',
      options: [{ label: 'HASH加密', value: 10 }, { label: '掩盖', value: -1 }],
    },
  },
  // {
  //   valueType: 'a-select',
  //   key: 'desensitizationModuleList',
  //   label: '脱敏模块',
  //   rules: [{ required: true, message: '请选择', trigger: 'blur' }],
  //   fieldProps: {
  //     placeholder: '请选择',
  //     options: desensitizationModuleListOptions,
  //     mode: 'multiple',
  //   },
  // },
  {
    valueType: 'a-select',
    key: 'algorithmId',
    label: '选择算法',
    rules: [{ required: true, message: '请选择', trigger: 'blur' }],

    fieldProps: {
      placeholder: '请选择',
      options: algorithmIdOptions,
    },
  },
  {
    valueType: 'a-select',
    key: 'maskingRule',
    label: '掩盖规则',
    hideInForm: maskingRuleHidden,
    rules: [{ required: true, message: '请选择', trigger: 'blur' }],

    fieldProps: {
      placeholder: '请选择',
      options: [
        { label: '保留前n后m', value: 20 },
        { label: '保留前n至后m', value: 21 },
        { label: '遮盖前n后m', value: 22 },
        { label: '遮盖前n至后m', value: 23 },
      ],
    },
  },

  {
    valueType: 'a-input',
    key: 'mvalue',
    label: 'm值',
    hideInForm: maskingRuleHidden,
    rules: [{ required: true, pattern: numberPattern, message: '请输入数字', trigger: 'blur' }],
    colProps: { span: 12 },
    fieldProps: {
      placeholder: '请输入',
    },
  },
  {
    valueType: 'a-input',
    key: 'nvalue',
    label: 'n值',
    colProps: { span: 12 },
    hideInForm: maskingRuleHidden,
    rules: [{ required: true, pattern: numberPattern, message: '请输入数字', trigger: 'blur' }],
    fieldProps: {
      placeholder: '请输入',
    },
  },
  {
    valueType: 'a-input',
    key: 'testData',
    label: '测试数据',
    fieldProps: {
      placeholder: '请输入',
    },
  },
  {
    valueType: 'a-input',
    key: 'resultData',
    label: '脱敏效果',
    fieldProps: {
      placeholder: '请输入',
    },

  },
]);
const title = computed(() => {
  return `${dataDrawerConfig && dataDrawerConfig.value.type === 'add' ? '新增' : '编辑'}配置`;
});
watch(open, (val) => {
  if (val) {
    onOpen();
  }
});
async function onOpen() {
  getOptions();
  if (dataDrawerConfig) {
    if (dataDrawerConfig.value.type === 'add') {
      form.value = cloneDeep({ ...defaultFormData });
    }
    else if (dataDrawerConfig.value.id) {
      postDataSecurityDesenTemplateDetail({ id: dataDrawerConfig.value.id }).then(({ data }) => {
        if (data) {
          let { desensitizationMethod, ...other } = data;
          let maskingRule;
          if (desensitizationMethod !== 10) {
            maskingRule = desensitizationMethod;
            desensitizationMethod = -1;
          }
          form.value = { ...other, desensitizationMethod, maskingRule };
        }
      });
    }
  }
}

function close() {
  open.value = false;
}
function getOptions() {
  postDataSecuritySensitiveLabelOptions({}).then(({ data }) => {
    sensitiveLabelIdOptions.value = data || [];
  });
  postDataSecuritySecurityClassOptions({}).then(({ data }) => {
    securityLevelIdOptions.value = data || [];
  });
  postDataSecurityEncryptionAlgorithmOptions({ typeCode: 3 }).then(({ data }) => {
    algorithmIdOptions.value = data || [];
  });
}
const submitLoading = ref(false);
const formRef = ref();
function handleTest(record: DataDrawerForm) {
  if (!record.testData) {
    message.error('请输入测试数据');
  }
  else {
    formRef.value.validate().then(() => {
      postDataSecurityDesenTemplateDesenTest({ ...formatterSubmitData(), name: record.testData } as API.DesensitizationTemplateDto).then(({ data }) => {
        form.value.resultData = data;
      });
    });
  }
}
function formatterSubmitData() {
  const { maskingRule, desensitizationMethod, testData, resultData, ...other } = form.value;
  return { ...other, desensitizationMethod: desensitizationMethod === -1 ? maskingRule : desensitizationMethod };
}
function onSubmit() {
  formRef.value.validate().then(() => {
    submitLoading.value = true;
    const request = dataDrawerConfig && dataDrawerConfig.value.type === 'add' ? postDataSecurityDesenTemplateSave : postDataSecurityDesenTemplateUpd;
    request(formatterSubmitData() as API.DesensitizationTemplateDto).then(() => {
      message.success('操作成功');
      open.value = false;
      emit('submit');
    }).finally(() => {
      submitLoading.value = false;
    });
  });
}
</script>

<template>
  <CustomDrawer
    v-model:open="open"
    :title="title"
    placement="right"
    :width="1000"
    destroy-on-close
    :footer-style="{ textAlign: 'right' }"
    @close="close"
  >
    <div class="drawer-content">
      <SchemaForm ref="formRef" v-model="form" :grid="true" :columns="columns" layout="vertical" :submitter="false">
        <template #formItem="{ column, record }">
          <template v-if="column.key === 'testData'">
            <div class="button-label">
              <div class="button-group">
                <a-button type="link" @click="handleTest(record)">
                  测试
                </a-button>
              </div>
            </div>
            <a-input v-model:value="record[column.key]" v-bind="column.fieldProps" />
          </template>
        </template>
      </SchemaForm>
    </div>
    <template #footer>
      <div class="button-group">
        <a-button @click="open = false">
          取消
        </a-button>
        <a-button type="primary" :loading="submitLoading" @click="onSubmit">
          提交
        </a-button>
      </div>
    </template>
  </CustomDrawer>
</template>

<style lang="scss" scoped>
.drawer-content {
  flex: 1;
  margin-top: 20px;
  overflow: hidden auto;
  // border-top: 1px dashed #e7e7e7;
}

.button-label {
  position: absolute;
  top: -32px;
  right: 5px;

  :deep(.ant-btn-link) {
    height: fit-content;
    padding: 0;
  }
}
</style>

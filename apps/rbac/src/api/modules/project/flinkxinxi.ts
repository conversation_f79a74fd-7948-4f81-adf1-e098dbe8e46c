import { basic as request } from '@/api/request';

/**
 * @description 新增flink
 * @url /info/flink/addFlink
 * @method POST
 * <AUTHOR>
 */
export function postInfoFlinkAddFlink<R = API.ResponseData, T = API.ResponseData>(
  body: API.FlinkMsgDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/info/flink/addFlink', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 删除flink
 * @url /info/flink/deletedFlink
 * @method POST
 * <AUTHOR>
 */
export function postInfoFlinkDeletedFlink<R = API.ResponseData, T = API.ResponseData>(
  body: API.FlinkMsgDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/info/flink/deletedFlink', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 查询flink信息
 * @url /info/flink/findAll
 * @method GET
 * <AUTHOR>
 */
export function getInfoFlinkFindAll<R = API.ResponseData, T = API.ResponseData>(
  options?: Parameters<typeof request.Get<R, T>>[1],
) {
  return request.Get<R, T>('/info/flink/findAll', {
    ...(options || {}),
  });
}

/**
 * @description 编辑flink
 * @url /info/flink/updateFlink
 * @method POST
 * <AUTHOR>
 */
export function postInfoFlinkUpdateFlink<R = API.ResponseData, T = API.ResponseData>(
  body: API.FlinkMsgDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/info/flink/updateFlink', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

import { basic as request } from '@/api/request';

/**
 * @description 新增数仓模型 新增数仓模型
 * @url /dataWarehouse/model/add
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseModelAdd<R = API.ResponseDataObject, T = API.ResponseDataObject>(
  body: API.DataWarehouseModelSave,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/dataWarehouse/model/add', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 模型所有字段
 * @url /dataWarehouse/model/columns
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseModelColumns<
  R = API.DataWarehouseModelColumns,
  T = API.DataWarehouseModelColumns,
>(body: API.Modelid, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWarehouse/model/columns', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 发布模型 数仓模型创建采集任务
 * @url /dataWarehouse/model/createTask
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseModelCreateTask<R = API.ResponseData, T = API.ResponseData>(
  body: API.DataMetaModelTaskDto,
  options?: Parameters<typeof request.Post<R, T>>[2],
) {
  return request.Post<R, T>('/dataWarehouse/model/createTask', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 数据源（数据仓专用） 数据源
 * @url /dataWarehouse/model/dataSources
 * @method GET
 * <AUTHOR>
 */
export function getDataWarehouseModelDataSources<
  R = API.ResponseDataListDataSource,
  T = API.ResponseDataListDataSource,
>(options?: Parameters<typeof request.Get<R, T>>[1]) {
  return request.Get<R, T>('/dataWarehouse/model/dataSources', {
    ...(options || {}),
  });
}

/**
 * @description 根据数仓分层查询数据源（数据仓专用） 根据数仓分层查询数据源
 * @url /dataWarehouse/model/dataSources/byLayerId
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseModelDataSourcesByLayerId<
  R = API.ResponseDataListDataSource,
  T = API.ResponseDataListDataSource,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWarehouse/model/dataSources/byLayerId', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 删除数仓模型 删除数仓模型
 * @url /dataWarehouse/model/delete
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseModelDelete<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWarehouse/model/delete', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 数仓模型详情 数仓模型详情
 * @url /dataWarehouse/model/detail
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseModelDetail<
  R = API.ResponseDataDataWarehouseModel,
  T = API.ResponseDataDataWarehouseModel,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWarehouse/model/detail', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 模型任务列表 模型任务列表
 * @url /dataWarehouse/model/mission/list
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseModelMissionList<
  R = API.ResponseDataPageDataDataMissionWarehouseDto,
  T = API.ResponseDataPageDataDataMissionWarehouseDto,
>(body: API.DataMissionWarehousePage, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWarehouse/model/mission/list', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 模型任务日志查询 模型任务日志查询，入参是任务id
 * @url /dataWarehouse/model/mission/log
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseModelMissionLog<
  R = API.ResponseDataString,
  T = API.ResponseDataString,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWarehouse/model/mission/log', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 模型任务重新执行 模型任务重新执行,入参是模型id
 * @url /dataWarehouse/model/mission/restart
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseModelMissionRestart<
  R = API.ResponseDataPageDataDataMissionWarehouseDto,
  T = API.ResponseDataPageDataDataMissionWarehouseDto,
>(body: API.IdBase, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWarehouse/model/mission/restart', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 数仓模型分页查询 数仓模型分页查询
 * @url /dataWarehouse/model/page
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseModelPage<
  R = API.ResponseDataPageDataDataWarehouseModelSimpleDto,
  T = API.ResponseDataPageDataDataWarehouseModelSimpleDto,
>(body: API.DataWarehouseModelPage, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWarehouse/model/page', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

/**
 * @description 数据板块-数据源（数据仓专用） 数据板块-数据源
 * @url /dataWarehouse/model/project/dataSources
 * @method GET
 * <AUTHOR>
 */
export function getDataWarehouseModelProjectDataSources<
  R = API.ResponseDataListProjectVo,
  T = API.ResponseDataListProjectVo,
>(options?: Parameters<typeof request.Get<R, T>>[1]) {
  return request.Get<R, T>('/dataWarehouse/model/project/dataSources', {
    ...(options || {}),
  });
}

/**
 * @description 修改数仓模型 修改数仓模型
 * @url /dataWarehouse/model/update
 * @method POST
 * <AUTHOR>
 */
export function postDataWarehouseModelUpdate<
  R = API.ResponseDataObject,
  T = API.ResponseDataObject,
>(body: API.DataWarehouseModelSave, options?: Parameters<typeof request.Post<R, T>>[2]) {
  return request.Post<R, T>('/dataWarehouse/model/update', body, {
    headers: {
      'Content-Type': 'application/json',
    },
    ...(options || {}),
  });
}

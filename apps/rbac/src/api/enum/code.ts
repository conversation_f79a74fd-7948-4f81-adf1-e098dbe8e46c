export enum RESPONSE_CODE {
  /**
   * @description
   * 接口请求成功
   */
  SUCCESS = 0,

  /**
   * @description
   * 运行异常
   */
  ERROR = -1,

  /**
   * @description
   * 操作失败,业务异常
   */
  BUSINESS_ERROR = 4,

  /**
   * @description
   * 账户锁定,请联系管理员
   */
  ACCOUNT_LOCKED = *********,

  /**
   * @description
   * 登录名或密码不正确
   */
  LOGINNAME_PASSWORD_WRONG = *********,

  /**
   * @description
   * 账户未启用,请联系管理员
   */
  ACCOUT_NOT_ENABLE = *********,

  /**
   * @description
   * token已过期
   */
  ACCESS_TOKEN_INVALID = *********,
  /**
   * @description
   * 刷新refreshToken已过期
   */
  LOGIN_TOKEN_INVALID = *********,

  /**
   * @description
   * 访问未授权
   */
  UNAUTHORIZED_ACCESS = *********,

  /**
   * @description
   * 验证码失效
   */
  CODE_OVERTIME = *********,

  /**
   * @description
   * 验证码不匹配
   */
  CODE_WRONG = *********,

  /**
   * @description
   * 首次登录,请修改初始密码
   */
  CHANGE_INIT_PASSWORD = *********,

  /**
   * @description
   * 登录密码已过期,请修改密码
   */
  PASSWORD_EXPIRED = *********,
}
